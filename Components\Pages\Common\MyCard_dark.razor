﻿@inject IConfiguration Configuration
@inject IJSRuntime JS
@rendermode InteractiveServer

<div class="card-container h-100">
    <div class="card @Css h-100">
        <div class="card-header d-flex justify-content-between align-items-center mt-2">
            <div class="d-flex align-items-center gap-2">
                <span class="badge bg-secondary fw-bold fs-5">@Year</span>
                <span class="fw-bold fs-5">@Title</span>
            </div>

            @if (IsModal)
            {
                <div>
                    @* 互動視窗表格 *@
                    <BS5Modal Id="@ModalId" Title="@($"{Title} - 數據表格")">
                        <ButtonContent>
                            <i class="fa-solid fa-table"></i>
                        </ButtonContent>

                        <ChildContent>
                            <TableExcel TableColumns="@TableColumns" TableData="@TableData" />
                        </ChildContent>
                    </BS5Modal>

                    @* 放大檢視 *@
                    <BS5Modal Id="@($"{ModalId}_chart_detail")" Title="@($"{Title} - 圖表詳細資訊")">
                        <ButtonContent>
                            <a href="javascript:void(0);" class="mx-2" title="放大檢視">
                                <i class="fa-solid fa-magnifying-glass-plus"></i>
                            </a>
                        </ButtonContent>

                        <ChildContent>
                            <div class="container-fluid">
                                <div class="row">
                                    <!-- 左側：圖表資訊 -->
                                    <div class="col-md-6">
                                        <div class="card bg-dark text-light mb-3">
                                            <div class="card-header">
                                                <h5 class="mb-0">📊 圖表基本資訊</h5>
                                            </div>
                                            <div class="card-body">
                                                <div class="row">
                                                    <div class="col-sm-4"><strong>圖表標題：</strong></div>
                                                    <div class="col-sm-8">@Title</div>
                                                </div>
                                                @if (!string.IsNullOrEmpty(Year))
                                                {
                                                    <div class="row mt-2">
                                                        <div class="col-sm-4"><strong>資料年度：</strong></div>
                                                        <div class="col-sm-8">@Year</div>
                                                    </div>
                                                }
                                                <div class="row mt-2">
                                                    <div class="col-sm-4"><strong>資料筆數：</strong></div>
                                                    <div class="col-sm-8">@(TableData?.Count ?? 0) 筆</div>
                                                </div>
                                                <div class="row mt-2">
                                                    <div class="col-sm-4"><strong>資料欄位：</strong></div>
                                                    <div class="col-sm-8">@(TableColumns?.Count ?? 0) 個欄位</div>
                                                </div>
                                                @if (!string.IsNullOrEmpty(ExcelUrl))
                                                {
                                                    <div class="row mt-2">
                                                        <div class="col-sm-4"><strong>資料來源：</strong></div>
                                                        <div class="col-sm-8">
                                                            <small class="text-muted">Excel 檔案</small>
                                                        </div>
                                                    </div>
                                                }
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 右側：統計資訊 -->
                                    <div class="col-md-6">
                                        <div class="card bg-dark text-light mb-3">
                                            <div class="card-header">
                                                <h5 class="mb-0">📈 數據統計</h5>
                                            </div>
                                            <div class="card-body">
                                                @if (TableData != null && TableData.Any())
                                                {
                                                    @foreach (var column in GetNumericColumns())
                                                    {
                                                        var stats = CalculateColumnStats(column);
                                                        <div class="mb-3">
                                                            <h6 class="text-info">@column</h6>
                                                            <div class="row small">
                                                                <div class="col-6">最大值：<span class="text-success">@stats.Max.ToString("N0")</span></div>
                                                                <div class="col-6">最小值：<span class="text-warning">@stats.Min.ToString("N0")</span></div>
                                                                <div class="col-6">平均值：<span class="text-primary">@stats.Average.ToString("N2")</span></div>
                                                                <div class="col-6">總計：<span class="text-light">@stats.Sum.ToString("N0")</span></div>
                                                            </div>
                                                        </div>
                                                    }
                                                }
                                                else
                                                {
                                                    <p class="text-muted">無數據可顯示統計資訊</p>
                                                }
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 底部：完整圖表 -->
                                <div class="row">
                                    <div class="col-12">
                                        <div class="card bg-dark text-light">
                                            <div class="card-header">
                                                <h5 class="mb-0">🔍 放大圖表檢視</h5>
                                            </div>
                                            <div class="card-body" style="height: 500px;">
                                                @ChartContent
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </ChildContent>
                    </BS5Modal>

                    @* 下載 *@
                    @if (!string.IsNullOrEmpty(ExcelUrl))
                    {
                        <a href="@GetDownloadUrl()" class="ms-2" download>
                            <i class="fa-solid fa-download"></i>
                        </a>
                    }
                    else if (OnExportExcel.HasDelegate)
                    {
                        <a href="javascript:void(0)" class="ms-2" @onclick="OnExportExcel" @onclick:preventDefault="true" title="匯出Excel">
                            <i class="fa-solid fa-download"></i>
                        </a>
                    }
                </div>
            }

        </div>
        <div class="card-body @bodyCss">
             @ChildContent
        </div>
    </div>
</div>


@code {
    [Parameter] public RenderFragment? ChildContent { get; set; }
    [Parameter] public RenderFragment? ChartContent { get; set; }
    [Parameter] public string? Title { get; set; }
    [Parameter] public string? Year { get; set; }
    [Parameter] public string? Css { get; set; }
    [Parameter] public string? bodyCss { get; set; }
    [Parameter] public bool IsModal { get; set; } = false;
    [Parameter] public string? ExcelUrl { get; set; }
    [Parameter] public string ModalId { get; set; } = "myModal";
    [Parameter] public EventCallback OnExportExcel { get; set; }
    [Parameter] public List<string>? TableColumns { get; set; }

    /// <summary>
    /// TableData 為 Dictionary 的 List，每筆 Dictionary 表示一列資料，key 是欄位名稱
    /// </summary>
    [Parameter] public List<Dictionary<string, object>>? TableData { get; set; }

    // 統計資訊結構
    public class ColumnStats
    {
        public decimal Max { get; set; }
        public decimal Min { get; set; }
        public decimal Average { get; set; }
        public decimal Sum { get; set; }
    }

    // 獲取數值型欄位
    private List<string> GetNumericColumns()
    {
        if (TableData == null || !TableData.Any() || TableColumns == null)
            return new List<string>();

        var numericColumns = new List<string>();
        var firstRow = TableData.First();

        foreach (var column in TableColumns)
        {
            if (firstRow.ContainsKey(column) && firstRow[column] != null)
            {
                var value = firstRow[column];
                if (IsNumericValue(value))
                {
                    numericColumns.Add(column);
                }
            }
        }

        return numericColumns;
    }

    // 檢查值是否為數值型
    private bool IsNumericValue(object value)
    {
        if (value == null) return false;

        return value is int || value is long || value is decimal ||
               value is double || value is float ||
               (decimal.TryParse(value.ToString(), out _));
    }

    // 計算欄位統計資訊
    private ColumnStats CalculateColumnStats(string columnName)
    {
        var stats = new ColumnStats();

        if (TableData == null || !TableData.Any())
            return stats;

        var values = new List<decimal>();

        foreach (var row in TableData)
        {
            if (row.ContainsKey(columnName) && row[columnName] != null)
            {
                if (decimal.TryParse(row[columnName].ToString(), out decimal value))
                {
                    values.Add(value);
                }
            }
        }

        if (values.Any())
        {
            stats.Max = values.Max();
            stats.Min = values.Min();
            stats.Average = values.Average();
            stats.Sum = values.Sum();
        }

        return stats;
    }

    /// <summary>
    /// 取得下載 URL，在正式環境時加上 Url 前綴
    /// </summary>
    private string GetDownloadUrl()
    {
        string apiUrl = Configuration["SysSetting:DownLoadUrl"] ?? "";
        string downloadUrl = $"{apiUrl}/Excel/download?url={Uri.EscapeDataString(ExcelUrl ?? "")}";

        return downloadUrl;
    }

    protected override void OnParametersSet()
    {
        base.OnParametersSet();
    }
}
