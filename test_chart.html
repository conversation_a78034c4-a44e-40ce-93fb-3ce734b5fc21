<!DOCTYPE html>
<html>
<head>
    <title>圖表測試</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .debug { background: #f0f0f0; padding: 10px; margin: 10px 0; }
    </style>
</head>
<body>
    <h1>圖表功能測試</h1>
    
    <div class="debug">
        <h3>測試步驟：</h3>
        <ol>
            <li>打開瀏覽器開發者工具 (F12)</li>
            <li>切換到 Console 標籤</li>
            <li>導航到 AzureCosmos 頁面</li>
            <li>觀察控制台輸出的調試信息</li>
            <li>點擊任一圖表的放大鏡按鈕</li>
            <li>檢查互動視窗中是否顯示圖表</li>
        </ol>
    </div>
    
    <div class="debug">
        <h3>預期的調試信息：</h3>
        <ul>
            <li>AzureChart: 過濾後的數據數量: [數字]，目標學校: 雲科</li>
            <li>AzureChart PrepareTimeSeriesData: 最終 internalChartData 數量: [數字]</li>
            <li>AzureChart: 數據加載完成，internalChartData 數量: [數字]</li>
        </ul>
    </div>
    
    <div class="debug">
        <h3>可能的問題：</h3>
        <ul>
            <li>如果 internalChartData 數量為 0，表示數據過濾有問題</li>
            <li>如果看到 "學校名稱 'xxx' 不包含目標 '雲科'"，表示學校名稱不匹配</li>
            <li>如果沒有調試信息，表示組件沒有正確初始化</li>
        </ul>
    </div>
    
    <script>
        console.log('測試頁面已載入，請導航到 AzureCosmos 頁面進行測試');
    </script>
</body>
</html>
